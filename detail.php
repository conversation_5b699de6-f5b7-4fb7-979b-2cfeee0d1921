<?php
  include $_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php';
  $q = "SELECT * FROM listing WHERE listingno = '".$_GET['selection']."' and display = 'Y' ";
  $r = mysqli_query($connection,$q) or die ("error :".mysqli_error()."<br /><br />".$q);
  $n = mysqli_num_rows($r);

  if($n>0) {
	  $row=mysqli_fetch_array($r);
  }

  $desc         = isset($comments) ? explode( '.', $comments ) : [];
  $views        = $views ?? '';
  $lakefrontage = $lakefrontage ?? '';
  $beach        = $beach ?? '';
  $association  = $association ?? '';
  $dock         = $dock ?? '';
?>
<html>
<head>
<title>Northern Homes Realty Inc. Listing Number <?=$_GET['selection'];?></title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="<? if(strlen($toptext)>=1) {echo $row['toptext']." - ";} ?> <? echo $desc[0].".";?>">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown,
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>
<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td class="sidenav2">
          	<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
          </td>
		<td width="100%">
      		<div align="center" id='content'>
               	<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
                    </div>
  	 			<? if(strlen($row['toptext'])>=1) {echo "<h2>".$row['toptext']."</h2>";} ?>
                    <h1>Listing Number: <?=$row['listingno'];?></h1>
        			<table border="0" cellspacing="0" cellpadding="0" width="800">
          			<tr>
						<td width="50%">
              <table width="320" border="0" cellspacing="0" cellpadding="0" align="center">
                <tr>
                  <td style='vertical-align:bottom;'>
			   <?

			   $qp = "SELECT * from bo_photos WHERE bo_photo_listing = '".$_GET['selection']."'
			   		ORDER BY bo_photo_primary DESC, bo_photo_order";
			   $rp = mysqli_query($connection,$qp) or die ("error : ".mysqli_error()."<br /><br />".$qp);
			   $np = mysqli_num_rows($rp);
			   $x = 1;
			   $pic = array(); // Initialize the $pic array

			   while($rowp= mysqli_fetch_array($rp)){
				   $pic[$x]['pic'] = $rowp['bo_photo_file'];
				   $pic[$x]['cap'] = $rowp['bo_photo_caption'];
				   $pic[$x]['prime'] = $rowp['bo_photo_primary'];
				   $x=$x+1;
			   }

			   if(isset($pic[1]) && $pic[1]['prime']=="Y"){

			   		$pic1sm = substr($pic[1]['pic'], 0, strlen($pic[1]['pic']) - 4)."sm.jpg";

					$pic1smd = getimagesize($_SERVER['DOCUMENT_ROOT'].'/photos/'.$pic1sm);

					/* calculates aspect ratio */
    					$aspect_ratio = $pic1smd[1] / $pic1smd[0];
    					/* sets new size */
    					$new_width = 300;
    					$new_height = abs(300 * $aspect_ratio);

					echo "<div style='width:300px;height:".$new_height."px;
						background-image: url(/photos/".$pic1sm.");
						background-size:300px ".$new_height."px;
						background-position: center center;

						filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='/photos/".$pic1sm."',sizingMethod='scale');

						-ms-filter: \"progid:DXImageTransform.Microsoft.AlphaImageLoader(src='/photos/".$pic1sm."',sizingMethod='scale')\";'

						class='boxshadow' >";

					if($row['status']=='sold' || $row['solddate']!='0000-00-00') {
						echo "<div class='sold'>";
							echo "<a href='/photos/".$pic[1]['pic']."'>";
								echo "<img src='/images/spacer.gif' width='300' height='".$new_height."' border='0'>";
							echo "</a>";
						echo "</div>";
					} elseif ($row['status']=='pending') {
						echo "<div class='pending'>";
							echo "<a href='/photos/".$pic[1]['pic']."'>";
								echo "<img src='/images/spacer.gif' width='300' height='".$new_height."' border='0'>";
							echo "</a>";
						echo "</div>";
					} else {
						echo "<a href='/photos/".$pic[1]['pic']."'>";
							echo "<img src='/images/spacer.gif' width='300' height='".$new_height."' border='0'>";
						echo "</a>";
					}

					echo "</div>";

				 } else {
					echo "<img src='/photos/unavailable.jpg' width='300' border='0'>";
			   	 }

			   ?>
                  <br />
                  <br />
                  </td>
                </tr>

                <tr>
                  <td>
                  <?
                  $pt = ''; // Initialize the $pt variable
                  if($np>1) {
				   $z=0;
				   $y=2;
				   $pt = '<table width="100%" border="0" cellspacing="0" cellpadding="5">';
          while ($y <= 3) {
            if ($z == 0) {
              $pt .= '<tr>';
            }
            if (isset($pic[$y]) && !empty($pic[$y]['pic'])) {
              $picsm = substr($pic[$y]['pic'], 0, -4) . "sm.jpg";
              $pt .= '<td width="50%">';
              $pt .= '<a href="/photos/' . $pic[$y]['pic'] . '" target="_blank">';
              $pt .= '<img src="/photos/' . $picsm . '" width="140" border="0" class="boxshadow">';
              $pt .= '</a>';
              $pt .= '<br />';
              $pt .= $pic[$y]['cap'] . "<br />";
              $pt .= '</td>';
            }
            $z++;
            if ($z == 2) {
              $pt .= '</tr>';
              $z = 0;
            }
            $y++;
          }
					$pt.= '</table>';
					}
					echo $pt;

					if($np>3) {
						echo "<center><font size=3><a href='/photopage.php?selection=".$_GET['selection']."'>Click here for more pictures</a></font></center>"; }
				?>
                  </td>
                </tr>
                <tr>
                  <td><?php
				  if($_GET['selection']=="D256") {
					  echo "<br /><center><font size=3><a href='https://samuraivirtualtours.com/tours/icehouse/' target='_blank'>Click here for virtual tour</a></font></center>"; }
				  ?></td>
                </tr>
              </table>
            </td>
            <td>
              <table width="100%" border="0" cellspacing="0" cellpadding="5" style='margin-left:30px;'>
                <tr>
                  <td><img src="/images/spacer.gif" width="100" height="1"></td>
                  <td><img src="/images/spacer.gif" width="10" height="1"></td>
                  <td><img src="/images/spacer.gif" width="100" height="1"></td>
                </tr>
                <tr>
                  <td align="right">Sale Price:</td>
                  <td></td>
                  <td>$ <? echo number_format($row['price'],2);?></td>
                </tr>
                <tr>
                  <td align="right">Taxes:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['taxes'];?></td>
                </tr>
                <tr>
                  <td align="right">Location: </td>
                  <td>&nbsp;</td>
                  <td><?=$row['city'];?></td>
                </tr>
                <tr>
                  <td align="right">County:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['county'];?></td>
                </tr>
                <tr>
                  <td align="right">Age:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['age'];?></td>
                </tr>
                <tr>
                  <td align="right">Design:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['design'];?></td>
                </tr>
                <tr>
                  <td align="right">Acreage:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['propsize'];?></td>
                </tr>
                <tr>
                  <td align="right">Square Footage:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['sqfoot'];?></td>
                </tr>
                <tr>
                  <td align="right"># of Bedrooms:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['bed'];?> </td>
                </tr>
                <tr>
                  <td align="right"> # of Bathrooms:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['bath'];?></td>
                </tr>
                <tr>
                  <td align="right">Basement:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['basement'];?></td>
                </tr>
                <tr>
                  <td align="right">Garage:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['garage'];?></td>
                </tr>
                <tr>
                  <td align="right">Water:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['water'];?></td>
                </tr>
                <tr>
                  <td align="right">Septic:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['sewer'];?></td>
                </tr>
                <tr>
                  <td align="right">Heat:</td>
                  <td>&nbsp;</td>
                  <td><?=$row['heat'];?></td>
                </tr>
                <? if(strlen($views)>=1) {
				 echo '<tr>
                  		<td  align="right">Views:</td>
                  		<td>&nbsp;</td>
                  		<td>'.$row['views'].'</td>
                	</tr>';} ?>
			 <tr>
                  <td align="right"> Rights:</td>
                  <td>&nbsp;</td>
                  <td>
                   	 <? 	if ($row['whatrights']=="lf") {echo "Lakefront";}
					if ($row['whatrights']=="lr") {echo "Lake Rights";}
					if ($row['whatrights']=="bf") {echo "Brookfront";}
					if ($row['whatrights']=="rf") {echo "Riverfront";}
					if ($row['whatrights']=="pf") {echo "Pond Front";}
					if ($row['whatrights']=="wv") {echo "views only";}
					if ($row['whatrights']=="n")  {echo "None";}
				?>
                  </td>
                </tr>
                <tr>
                  <td align="right">Season:
                  </td>
                  <td>&nbsp;</td>
                  <td>
                    <? if($row['season'] == "yr") { echo "Year-Round";}
				   if($row['season'] == "s")  { echo "Seasonal";}
				?>
                  </td>
                </tr>
                <? if(strlen($lakefrontage)>=1) {
				 echo '<tr>
                  <td align="right">Water Frontage:</td>
                  <td>&nbsp;</td>
                  <td>'.$row['lakefrontage'].'</td>
                </tr>';} ?>
                <? if(strlen($beach)>=1) {
				 echo '<tr>
                  <td align="right">Beach:</td>
                  <td>&nbsp;</td>
                  <td>'.$row['beach'].'</td>
                </tr>';} ?>
                <? if(strlen($association)>=1) {
				 echo '<tr>
                  <td align="right">Association:</td>
                  <td>&nbsp;</td>
                  <td>'.$row['association'].'</td>
                </tr>';} ?>
                <? if(strlen($dock)>=1) {
				 echo '<tr>
                  <td align="right">Dock:</td>
                  <td>&nbsp;</td>
                  <td>'.$row['dock'].'</td>
                </tr>';} ?>
              </table>
            </td>
          </tr>
          <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
          </tr>
          <tr>
            <td colspan="2"><font class="lilbigger">
              <? print nl2br($row['comments']); ?></font>
            </td>
          </tr>
          <tr>
               <td>&nbsp;</td>
               <td>&nbsp;</td>
          </tr>
          <tr>
            <td colspan="2" align="center"><a href="javascript:window.print()">
		  <b>click here for printable version</b></a></td>
          </tr>
          <tr>
            <td colspan="2">&nbsp;</td>
          </tr>
          <tr>
            <td colspan="2" align="center">
              <a href="javascript:history.go(-1)">Click here
                to view other properties in this category</a>
            </td>
          </tr>
        </table>
        <br />
		For more information contact:<br />
		Deirdre Schrader, Broker/Owner<br /><br />
        	NORTHERN HOMES REALTY, INC.<br />
          889 US Route 9<br />
        	Schroon Lake, NY 12870
        <p>(************* (phone);&nbsp;&nbsp; (************* (fax)</p>
        <p>email
          <a href="mailto:<EMAIL>" name="web inquiry - listing no <? echo "$listingno";?> " ><EMAIL></a>
          for more information</p>
    </td>
  </tr>
  <tr>
    <td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?>
    </td>
  </tr>
</table>
</body>
</html>
