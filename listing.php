<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate  Listings : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="Some of our greatest listings at Northern Home Realty.  Be sure to check back often for more amazing properties.">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>
<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
     	<td class="sidenav2">
			<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
		</td>
		<td width="100%"> 
		<?php
		
			if($_GET['t']=="res" || $_GET['t']=="com") { 
				$search = " zone LIKE '%".$_GET['t']."%' AND vacant='N' "; }
			if($_GET['t']=="v") {
				$search = " vacant='Y' "; }
			if($_GET['t']=="wf") {
				$search = " whatrights !='N' "; }
			if($_GET['t']=="inc") {
				$search = " income!='' ";}
			if($_GET['t']=="sl") {
				$search = " city='Schroon Lake' ";}	
			
			if (!$_GET['t'])  {
				$search = " zone LIKE '%res%' ";}
	
			$date = date("Y-m-d");
			
			if ($_GET['sort']) { $sort = "ORDER BY ".$_GET['sort']; } else { $sort = " ORDER BY uniqueid DESC "; }
	
			$q = "SELECT * FROM listing WHERE ".$search." AND display='y' ".$sort." ";
			$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br /><br />".$q);
			$n = mysqli_num_rows($r);

		?>
		<div align="center" id="content">
          	<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
               </div>
          	<h1><?php  
			  if ($_GET['t']=="res") { echo "Residential Listings"; }
			  if ($_GET['t']=="com") { echo "Commercial Listings"; }
			  if ($_GET['t']=="wf")  { echo "Water Rights and Water Views Listings"; }
			  if ($_GET['t']=="v")   { echo "Vacant Land Listings"; }
			  if ($_GET['t']=="inc") { echo "Income Properties"; }
			  if ($_GET['t']=="sl")  { echo "Schroon Lake Listings"; }
			?></h1>
	  <strong>You can sort the listings by clicking on a column heading.</strong><br /><br />
        Click on a Listing Number to see the detail of that listing.<br /><br /></div>
	<table border="0" cellspacing="0" cellpadding="0" align="center" width="800">
		<tr class="shadow"> 
               <td><img src="/images/spacer.gif" height="30" width="1"></td>
               <td class='mid' nowrap='nowrap'><a href="?sort=listingno&t=<?=$_GET['t'];?>">Listing No.</a></td>
               <td class='mid'><a href="?sort=toptext&t=<?=$_GET['t'];?>">Description</a></td>
               <td class='mid'><a href="?sort=city&t=<?=$_GET['t'];?>">Location</a></td>
               <td class='mid'><a href="?sort=price&t=<?=$_GET['t'];?>">Price</a></td>
		</tr>
        	<?
			$curclass="lite";
			if($n>0) {	
			while($row = mysqli_fetch_array($r)){
		
			echo "<tr class='".$curclass."'>";
				echo "<td>";
				
				$qp = "SELECT * FROM bo_photos WHERE bo_photo_listing = '".$row['listingno']."' AND bo_photo_primary = 'Y' ";
				$rp = mysqli_query($connection,$qp) or die ("error : ".mysqli_error()."<br /><br />".$qp);
				$np = mysqli_num_rows($rp);
				
				if($np>0) { 
					$rowp = mysqli_fetch_array($rp);
					$picsm = substr($rowp['bo_photo_file'], 0, strlen($rowp['bo_photo_file']) - 4)."sm.jpg";
					
					$pic = getimagesize($_SERVER['DOCUMENT_ROOT'].'/photos/'.$picsm);
					
					/* calculates aspect ratio */
    					$aspect_ratio = $pic[1] / $pic[0];
    					/* sets new size */
    					$new_width = 150;
    					$new_height = abs(150 * $aspect_ratio);
					
					//echo "<img src='/photos/".$picsm."' width='125' border='0'></a>";
					
					echo "<div style='width:150px;height:".$new_height."px;
						background-image: url(/photos/".$picsm.");
						background-size:150px ".$new_height."px;
						background-position: center center;
						
						filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='/photos/".$picsm."',sizingMethod='scale');
						
						-ms-filter: \"progid:DXImageTransform.Microsoft.AlphaImageLoader(src='/photos/".$picsm."',sizingMethod='scale')\";'>";
					
					if($row['status']=='sold' || $row['solddate']!='0000-00-00') { 
						echo "<div class='sold'>";
							echo "<a href='/detail.php?selection=".$row['listingno']."'>";
								echo "<img src='/images/spacer.gif' width='150' height='".$new_height."' border='0'>";
							echo "</a>";
						echo "</div>"; 
					} elseif ($row['status']=='pending') {
						echo "<div class='pending'>";
							echo "<a href='/detail.php?selection=".$row['listingno']."'>";
								echo "<img src='/images/spacer.gif' width='150' height='".$new_height."' border='0'>";
							echo "</a>";
						echo "</div>"; 					
					} else {
						echo "<a href='/detail.php?selection=".$row['listingno']."'>";
							echo "<img src='/images/spacer.gif' width='150' height='".$new_height."' border='0'>";
						echo "</a>"; 
					}
					
					echo "</div>";	
					
				} else {
					echo "<a href='/detail.php?selection=".$row['listingno']."'>";
						echo "<img src='/photos/unavailablesm.jpg' width='150' border='0'>";
					echo "</a>";
				}
				
				/////

				
				echo "</td>";
				
				
				/// ORIGINAL ///
				//echo "<a href='/detail.php?selection=".$row['listingno']."'>";
				//
				//$qp = "SELECT * FROM bo_photos WHERE bo_photo_listing = '".$row['listingno']."' AND bo_photo_primary = 'Y' ";
				//$rp = mysqli_query($connection,$qp) or die ("error : ".mysqli_error()."<br /><br />".$qp);
				//$np = mysqli_num_rows($rp);
				//
				//if($np>0) { 
				//	$rowp = mysqli_fetch_array($rp);
				//	$picsm = substr($rowp['bo_photo_file'], 0, strlen($rowp['bo_photo_file']) - 4)."sm.jpg";
				//	
				//	echo "<img src='/photos/".$picsm."' width='125' border='0'></a>";
				//} else {
				//	echo "<img src='/photos/unavailablesm.jpg' width='125' border='0'>";
				//}
				//echo "</a>";
				//////////////
				
				echo "</td>";
				echo "<td class='mid'><a href='/detail.php?selection=".$row['listingno']."' style='text-decoration:underline;'>".$row['listingno']."</a></td>";
				echo "<td class='mid'><font class='";
					if($row['solddate']!='0000-00-00' OR $row['solddate'] != NULL ) { echo "soldstatus"; } else { echo "status"; }
					echo"'>".$row['toptext']."</font><br />";
				
					if($_GET['t']=="res") { 
						echo "<img src='/images/spacer.gif' height='10' width='10'><br />";
						if($row['bed']!="0") { 
							echo "Bedrooms: ".$row['bed']." / Bathrooms: ".$row['bath']."<br />"; 
							echo "<img src='/images/spacer.gif' height='5' width='5'><br />"; }
						if($row['sqfoot']!="0") { echo "Square Feet: ".$row['sqfoot']; }
					}
					
					if($_GET['t']=="com") { 
						echo "<img src='/images/spacer.gif' height='10' width='10'><br />";
						if($row['income']!="") { 
							echo "Type: ".$row['income']."<br />";
							echo "<img src='/images/spacer.gif' height='5' width='5'><br />"; }
						if($row['noofunits']!="") { 
							echo "Number of Units: ".$row['noofunits']."<br />";
							echo "<img src='/images/spacer.gif' height='5' width='5'><br />"; }
						if($row['sqfoot']!="0") { echo "Square Feet: ".$row['sqfoot']; }
					}
					
					if($_GET['t']=="inc" || $_GET['t']=="wf" || $_GET['t']=="sl") {
						echo "<img src='/images/spacer.gif' height='10' width='10'><br />";
						if($row['bed']!="0") { 
							echo "Bedrooms: ".$row['bed']." / Bathrooms: ".$row['bath']."<br />"; 
							echo "<img src='/images/spacer.gif' height='5' width='5'><br />"; }
						if($row['sqfoot']!="0") { 
							echo "Square Feet: ".$row['sqfoot']."<br />";
							echo "<img src='/images/spacer.gif' height='5' width='5'><br />"; }
						if($row['vacant']=="Y") { 
							echo "Property Size: ".$row['propsize']." acres"; }
					}
					
					if($_GET['t']=="v") {
						echo "<img src='/images/spacer.gif' height='10' width='10'><br />";
						echo "Property Size: ".$row['propsize']." acres";
					}
				
				echo "</td>";
				echo "<td class='mid' nowrap='nowrap'>".$row['city']."</td>";
				echo "<td class='mid' nowrap='nowrap'>$ ".number_format($row['price'],0)."</td>";
			echo "</tr>";

			if ($curclass == "lite") { $curclass = "dark"; }
			elseif ($curclass == "dark") { $curclass = "lite";}
			}
		}
		?>
		</table>
          <br />
          <br />
	</td>
	</tr>
	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>