<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate  Listings : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="title" content="Information about the Schroon Lake Area">
<meta name="description" content="There's lots of great activities and seasonal fun going on in Schroon Lake and Northern Homes Realty is happy to share information on all our great events with you.">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>

<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr> 
		<td class="sidenav2">
          	<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
		</td>
		<td width="100%">
			<div id='content'>
               	<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
                    </div> 
				<?php
                    echo "<table width='800' cellpadding='0' cellspacing='0' border='0'>";
                    echo "<tr><td><img src='/images/spacer.gif' width='20'></td>";
                    echo "<td>";
                    
                         $query = "SELECT * from areainfo WHERE display='Y' AND blurbtype='info' ORDER BY orderno";
                         $result = mysqli_query($connection,$query) or die ("Can't do query");
                         $nrow = mysqli_num_rows($result);
                         
                         if($nrow>0) {
                         while($row = mysqli_fetch_array($result)){
                              echo "<h1>".$row['title']."</h1>";
                              echo "<blockquote>";
                              echo nl2br($row['info']);
                              echo "</blockquote>";
                              }
                         }
                                        
                         echo "</td>";
                         echo "<td><img src='/images/spacer.gif' width='20'></td></tr>";
                         echo "</table>";
                         
                    ?>
				<br />
          	</div>
		</td>
	</tr>
	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>