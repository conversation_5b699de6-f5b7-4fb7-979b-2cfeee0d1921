<?php
include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
session_start();

$today      = date("Y-m-d");
$_GET['op'] ??= '';
$message    ??= '';

if($_SESSION['authorizednhr']!="yes") {

	if ($_GET['option']=="check") {

		if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
			$_SESSION['user'] = $_POST['username'];
			$_SESSION['authorizednhr'] = "yes";
			header ('Location: index.php');
		} else {
				session_destroy();
				$msg= "username/password combo not correct";
		}
	}

?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {

	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            return $name;}

//// add
if($_GET['op']=="add") {
	/// if file upload present.. do second
	if($_FILES['photo']['tmp_name']) {
		try {
			$date = date("ymdHis");
			$picno = ord($_POST['order'])-64;

			$destination_file = $date."-".$picno.".jpg";

			$conn_id = ftp_connect($ftp_server);
			$login_result = ftp_login($conn_id, $ftp_user, $ftp_pass);

			if (!$conn_id || !$login_result) {
				die("FTP connection has failed!<br>Attempted to connect to $ftp_server");
			}

			$upload = ftp_put($conn_id, "public_html/photos/".$destination_file, $_FILES['photo']['tmp_name'], FTP_BINARY);

			if (!$upload) {
				die("Picture upload has failed!<br>Please try again.<br>");
			}

			// create thumbnail
			// thumbnail
			$imagefile  = "/home/<USER>/public_html/photos/".$destination_file;
			$imagefile3 = "/home/<USER>/public_html/photos/".$date."-".$picno."sm.jpg";

			if (!file_exists($imagefile)) {
				throw new Exception('File does not exist.');
			}

			if (!is_readable($imagefile)) {
				throw new Exception('File is not readable.');
			}

			$src_img = imagecreatefromjpeg($imagefile);

			if (!$src_img) {
				throw new Exception('Failed to create image from JPEG.');
			}

			$width = imagesx($src_img);
			$height = imagesy($src_img);

			if ($width > $height) {
				$new_w = 300;
				$new_h = 225;
			} else {
				$new_w = 225;
				$new_h = 300;
			}

			$src_img = imagecreatefromjpeg($imagefile);
			/* desired width of the thumbnail */
			$picsize = 300;
			/* grabs the height and width */
			$new_w = imagesx($src_img);
			$new_h = imagesy($src_img);
			/* calculates aspect ratio */
			$aspect_ratio = $new_h / $new_w;
			/* sets new size */
			$new_w = $picsize;
			$new_h = abs($new_w * $aspect_ratio);
			/* creates new image of that size */
			$dst_img = imagecreatetruecolor($new_w,$new_h);
			/* copies resized portion of original image into new image */
			imagecopyresampled($dst_img,$src_img,0,0,0,0,$new_w,$new_h,imagesx($src_img),imagesy($src_img));

			// ken redid this 1/13/06
			// added additional upload so permissions could be modified on this file and then overwritten with imagejpeg function
			$destination_filez = $date."-".$picno."sm.jpg";
			$upload2 = ftp_put($conn_id, "public_html/photos/".$destination_filez , $_FILES['photo']['tmp_name'], FTP_BINARY);

			$file = 'public_html/photos/'.$destination_filez;
			// sets permissions for writing on the uploaded file
			$chmod_cmd="CHMOD 0777 ".$file;
			$chmod=ftp_site($conn_id, $chmod_cmd);

			/* return jpeg data back to browser */
			imagejpeg($dst_img, $imagefile3);

			// sets permissions back
			$chmod_cmd="CHMOD 0755 ".$file;
			$chmod=ftp_site($conn_id, $chmod_cmd);
		} catch (\Throwable $th) {
			die($th);
		}

	  	$q = "INSERT INTO bo_photos SET
			 bo_photo_listing = '".$_GET['selection']."',
			 bo_photo_file = '".$destination_file."',
			 bo_photo_caption = '".stripinput($_POST['caption'])."',
			 bo_photo_order = '".stripinput($_POST['order'])."' ";
		if($_POST['order']=="A") { $q.= " , bo_photo_primary = 'Y' "; } else { $q.= " , bo_photo_primary = 'N' "; }
		$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br /><br />".$q);

		$message = "Update completed >>><br />";
	}
}

//// edit
if($_GET['op']=="edit") {
	$q = "UPDATE bo_photos SET
		bo_photo_caption = '".stripinput($_POST['caption'])."',
		bo_photo_order = '".stripinput($_POST['order'])."' ";
		if($_POST['order']=="A") { $q.= " , bo_photo_primary = 'Y' "; } else { $q.= " , bo_photo_primary = 'N' "; }

	$q.= " WHERE bo_photo_id = '".$_POST['uid']."' ";
	$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br /><br />".$q);

	echo "Update completed >>><br />";
}

//// delete
if($_GET['op']=="delete") {
	$q = "SELECT * FROM bo_photos WHERE bo_photo_id = '".$_POST['uid']."' ";
	$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br><br>".$q);
	$n = mysqli_num_rows($r);

	if($n>0) {
		$conn_id      = ftp_connect($ftp_server);
		$login_result = ftp_login($conn_id, "northern", "DandJ071519Q$");

		if (!$conn_id || !$login_result) {
			die("FTP failed: Attempted to connect to $ftp_server");
		}

		$row = mysqli_fetch_array($r);
			$al_picturesm = substr($row['bo_photo_file'], 0, strlen($row['bo_photo_file'])-4)."sm.jpg";
			$dftp_filepath = "public_html/photos/".$row['bo_photo_file'];
			$dftp_filepath2 = "public_html/photos/".$al_picturesm;

			$delete = ftp_delete($conn_id, "$dftp_filepath");
			$delete2 = ftp_delete($conn_id, "$dftp_filepath2");

			$query = "DELETE FROM bo_photos WHERE bo_photo_id ='".$_POST['uid']."' ";
			$results = mysqli_query($connection,$query);

			echo "Photo Deleted >>><br/>";
	}
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php');

	echo "<h1>Add Photos to Listing: ".$_REQUEST['selection']."</h1>";

	if($message!="") { echo $message."<br /><br />"; }

	$q = "SELECT * FROM bo_photos WHERE bo_photo_listing = '".$_GET['selection']."'
		ORDER BY bo_photo_primary DESC, bo_photo_order";
	$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br /><br />".$q);
	$n = mysqli_num_rows($r);

	   echo "<table>";
	   echo "<form method='post' action='?op=add&selection=".$_GET['selection']."' ENCTYPE='multipart/form-data'>";
	   echo "<input type='hidden' name='listingid' value='".$_GET['selection']."'>";
	   echo "<tr><td>PHOTO:</td><td><input type='file' name='photo'></td></tr>";
	   echo "<tr><td>CAPTION:</td><td><input type='text' name='caption'></td></tr>";
	   echo "<tr><td>ORDER:</td><td><input type='text' name='order' size='5' value='".chr($n + 65)."'> * (use 'A' - 'Z')</td></tr>";
	   echo "<tr><td>&nbsp;</td><td>* Photos with 'A' will display as the primary photo for each listing.<br />* One primary photo per listing.</td></tr>";
	   echo "<tr><td colspan='2'><input type='submit' name='submit'></td></tr>";
	   echo "</form>";
	   echo "</table>";
	   echo "<br /><br />";

	  if($n>0) {
	   echo "<strong>Current Photos For This Listing:</strong><br />";

	   echo "<table border='0' cellspacing='5' cellpadding='3'>";
	   while($row = mysqli_fetch_array($r)) {
		  echo "<form method='post' action='?op=edit&selection=".$_GET['selection']."'>";
		  echo "<input type='hidden' name='uid' value='".$row['bo_photo_id']."'>";
		  echo "<tr>";
			$picsm = substr($row['bo_photo_file'], 0, strlen($row['bo_photo_file']) - 4)."sm.jpg";
			echo "<td style='border-bottom:1px solid #3f7244' rowspan='2'>";
				echo "<a href='/photos/".$row['bo_photo_file']."'>";
				echo "<img src='/photos/".$picsm."' width='150' border='0'>";
				echo "</a>";
			echo "</td>";
			echo "<td style='border-bottom:1px solid #3f7244' rowspan='2'>";
				echo "Caption:<br />
					<input type='text' name='caption' value='".$row['bo_photo_caption']."'><br />";
				echo "Order:<br />
				<input type='text' name='order' style='width:20px' value='".$row['bo_photo_order']."'><br />";
			echo "</td>";
			echo "<td>";
				echo "<input type='submit' value='update'>";
			echo "</td>";
			echo "</form>";
		echo "</tr>";
		echo "<tr>";
			echo "<form method='post' action='?op=delete&selection=".$_GET['selection']."' onSubmit=\"return confirm('Are you sure you want to permanently delete this photo?');\">";
			echo "<input type='hidden' name='uid' value='".$row['bo_photo_id']."'>";
			echo "<td style='border-bottom:1px solid #3f7244'>";
				echo "<input type='submit' value='delete'>";
			echo "</form>";
		  echo "</tr>";

	  }
	  echo "</table>";
	  	}
?>
<br /><br />
</div>
</body>
</html>
<?
}
?>
