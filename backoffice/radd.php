<?php 
	include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
	include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
	session_start();
  	$today = date("Y-m-d");
  
  
	if($_SESSION['authorizednhr']!="yes") {
		
		if ($_GET['option']=="check") {

			if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
				$_SESSION['user'] = $_POST['username'];
				$_SESSION['authorizednhr'] = "yes";
				header ('Location: index.php');
			} else {
				 session_destroy();
				 $msg= "username/password combo not correct";
			}
		}
  
  ?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {
		
	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            return $name;}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
<style>
.r	{text-align:right; border-right:1px dotted #ffffcc;}
#addform tr:nth-child(odd)		{ background-color: #5e1400;}
#addform tr:nth-child(even)		{  }
#addform td	{vertical-align:middle;}
</style>
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php 
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php'); 
	
	if($_GET['sel']) { 
		$todo="edit";
		$q = "SELECT * FROM rental WHERE uniqueid = '".$_GET['sel']."' ";
		$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error()."<br /><br />".$q);
		$n = mysqli_num_rows($r);
		
		if($n>0) { $row = mysqli_fetch_array($r); } 
	
	} else { $todo="add"; }
	

	echo "<h1>Rental Listings (".$todo.")</h1>";
	
	echo "<form method='post' action='rindex.php?option=".$todo."' ENCTYPE='multipart/form-data'>";
	if($_GET['sel']) {
		echo "<input type='hidden' name='uniqueid' value='".$row['uniqueid']."'>";	
	}
	
	echo "<table border='0' cellspacing='0' cellpadding='3' id='addform'>";
		echo "<tr>";
			echo "<td class='r'>Listing</td>";
			echo "<td><input type='text' name='listingno' size='4' value='".$row['listingno']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Description</td>";
			echo "<td><input type='text' name='toptext' size='60' value='".$row['toptext']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Type of Rental</td>";
			echo "<td><input type='checkbox' name='type1' value='s'";
				if(strstr($row['type'],'s')) { echo " checked "; }
			echo ">vacation rental<br />
					<input type='checkbox' name='type2' value='r'";
				if(strstr($row['type'],'r')) { echo " checked "; }		
			echo ">other rental<br /></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Address</td>";
			echo "<td><input type='text' name='address' size='25' value='".$row['address']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>City/State/Zip</td>";
			echo "<td><input type='text' name='city' size='20' value='".$row['city']."'>/<input type='text' name='state' size='2' value='".$row['state']."'>/<input type='text' name='zip' size='10' value='".$row['zip']."'></td>";
		echo "</tr>";
echo "<tr>";
			echo "<td class='r'>Contact Name</td>";
			echo "<td><input type='text' name='contactname' size='20' value='".$row['contactname']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Contact Address</td>";
      		echo "<td><input type='text' name='contactaddress' size='20' value='".$row['contactaddress']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Contact City/State/Zip</td>";
			echo "<td><input type='text' name='contactcity' size='20' value='".$row['contactcity']."'>/<input type='text' name='contactstate' size='2' value='".$row['contactstate']."'>/<input type='text' name='contactzip' size='10' value='".$row['contactzip']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Listing Date</td>";
			echo "<td><input type='text' name='listingdate' size='10' value='".$row['listingdate']."'> (YYYY-MM-DD)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Expiration Date</td>";
			echo "<td><input type='text' name='expdate' size='10' value='".$row['expdate']."'> (YYYY-MM-DD)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Price</td>";
			echo "<td><input type='text' name='price' size='10' value='".$row['price']."'> (numbers only - no decimal)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Per</td>";
			echo "<td><input type='text' name='per' size='10' value='".$row['per']."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No of Bedrooms</td>";
			echo "<td><input type='text' name='bed' size='4' value='".$row['bed']."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No of Bathrooms</td>";
			echo "<td><input type='text' name='bath' size='4' value='".$row['bath']."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Square Feet</td>";
			echo "<td><input type='text' name='sqfoot' size='10' value='".$row['sqfoot']."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No. of Guests</td>";
			echo "<td><input type='text' name='noofguest' size='4' value='".$row['noofguest']."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Beach</td>";
			echo "<td><select name='beach'>";
				echo "<option value='N'";
					if($row['beach']=="N") { echo " selected "; }
				echo ">N</option>";
				echo "<option value='Y'";
					if($row['beach']=="Y") { echo " selected "; }
				echo ">Y</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Dock</td>";
			echo "<td><select name='dock'>";
				echo "<option value='N'";
					if($row['dock']=="N") { echo " selected "; }
				echo ">N</option>";
				echo "<option value='Y'";
					if($row['dock']=="Y") { echo " selected "; }
				echo ">Y</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Views</td>";
			echo "<td><input type='text' name='views' size='20' value='".$row['views']."'></td>";
		echo "</tr>";
	
		echo "<tr>";
			echo "<td class='r'>Dates Available</td>";
			echo "<td><textarea name='datesavail' rows='2' cols='60'>".$row['datesavail']."</textarea></td>";
		echo "</tr>";
	 
	 	echo "<tr>";
			echo "<td class='r'>Association</td>";
			echo "<td><input type='text' name='association' size='15' value='".$row['association']."'></td>";
		echo "</tr>";
 
		echo "<tr>";
			echo "<td class='r'>Rights</td>";
			echo "<td><select name='whatrights'>";
				echo "<option value='n'";
					if($row['whatrights']=="n") { echo " selected "; }
				echo ">n/a</option>";
				echo "<option value='lf'";
					if($row['whatrights']=="lf") { echo " selected "; }
				echo ">Lakefront</option>";
				echo "<option value='lr'";
					if($row['whatrights']=="lr") { echo " selected "; }
				echo ">Lakerights</option>";
				echo "<option value='bf'";
					if($row['whatrights']=="bf") { echo " selected "; }
				echo ">Brookfront</option>";
				echo "<option value='rf'";
					if($row['whatrights']=="rf") { echo " selected "; }
				echo ">Riverfront</option>";
				echo "<option value='pf'";
					if($row['whatrights']=="pf") { echo " selected "; }
				echo ">Pond Front</option>";
				echo "<option value='wv'";
					if($row['whatrights']=="wv") { echo " selected "; }
				echo">Water Views</option>";
			echo "</select></td>";
		echo "</tr>";
    
 		echo "<tr>";
			echo "<td class='r'>Comments</td>";
			echo "<td><textarea name='comments' rows='7' cols='50'>".$row['comments']."</textarea></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Listing Agent</td>";
			echo "<td><input type='text' name='listingagent' size='10' value='".$row['listingagent']."'></td>";
		echo "</tr>";
		   
		echo "<tr>";
			echo "<td class='r'>Show</td>";
			echo "<td><select name='display'>";
				echo "<option value='Y'";
					if($row['display']=="Y") { echo " selected "; }
				echo ">Y</option>";
				echo "<option value='N'";
					if($row['display']=="N") { echo " selected "; }
				echo ">N</option>";
			echo "</select></td>";
		echo "</tr>";	
		echo "<tr>";
			echo "<td>&nbsp;</td>";
			echo "<td><input type='submit' value='".$todo." listing'></td>";
		echo "</tr>";
		echo "</table>";
		echo "</form>";
?>
<br/><br/>
</div>
</body>
</html>
<?
}
?> 