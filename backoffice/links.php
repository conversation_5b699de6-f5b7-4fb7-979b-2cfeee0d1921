<?php
include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
session_start();

$today = date("Y-m-d");
$_GET['option'] ??= '';
$_GET['sort'] ??= '';

if($_SESSION['authorizednhr']!="yes") {

	if ($_GET['option']=="check") {

		if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
			$_SESSION['user'] = $_POST['username'];
			$_SESSION['authorizednhr'] = "yes";
			header ('Location: index.php');
		} else {
				session_destroy();
				$msg= "username/password combo not correct";
		}
	}

?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {

	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            return $name;}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
<?php
  function removeEvilAttributes($tagSource)
{	$stripAttrib = "'(style|class)=\"(.*?)\"'i";
	$tagSource = stripslashes($tagSource);
	$tagSource = preg_replace($stripAttrib, '', $tagSource);
	return $tagSource;
}


/////////////////////////////////////////////////////////////////////////////////////////////
function removeEvilTags($source)
{	$allowedTags='<a><br><b><i><em><strong><u><img><hr>'.
			 '<table><tr><td><th>'.
			 '<sup><sub><strike><ul><ol><li><font><center>';
	$source = strip_tags($source, $allowedTags);
	return preg_replace('/<(.*?)>/ie', "'<'.removeEvilAttributes('\\1').'>'", $source);
}


/////////////////////////////////////////////////////////////////////////////////////////////
function stripquery($name){
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            $name = stripslashes($name);
            return $name;}
?>
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php');

	echo "<table width='970' cellpadding='0' cellspacing='0' border='0'>";
		echo "<tr>";
			echo "<td><h1>Links</h1></td>";
			echo "<form method='post' action='?option=add'>";
			echo "<td style='text-align:right;vertical-align:middle;'><input type='submit' value='add new link'></td>";
			echo "</form>";
		echo "</tr>";
	echo "</table>";


//////////////////////////////////////////////////////////////////////////////////////////////

/////////////////////// ADD ////////////////////
if($_GET['option']=='add') {

	echo "<font class='h1'>add a new link</font><hr>";
	echo "<form method='post' action='?option=processadd'>";
	echo "<table width='300' border=0 cellspacing=0 cellpadding=5>";
	echo "<tr><td>Link Name</td><td><input class='field' type='text' name='link_name' size=45></td></tr>";
	echo "<tr><td>Link Address</td><td><input class='field' type='text' name='link_address' value='www.' size=45></td></tr>";
	echo "<tr><td>Description</td><td><textarea name='link_description' rows='6' cols='40'></textarea></td></tr>";
	echo "<tr><td>Link Type</td><td><select name='link_code'>
									<option value='a-sl'>Schroon Lake</option>
									<option value='b-aa'>Area Attraction</option>
									<option value='c-li'>Links of Interest</option>
									</select>
									</td></tr>";
	echo "<tr><td>Order</td><td><input type='text' name='link_display_order'></td></tr>";
	echo "<tr><td>Show</td><td><input type='radio' name='active' value='Y' checked>Y<br>
									<input type='radio' name='active' value='N'>N</td></tr>";
	echo "<tr><td>&nbsp;</td><td></td></tr>";
	echo "<tr><td>&nbsp;</td>
		<td><input class='button' type='submit' name='submit' value='add entry'></td></tr>";
	echo "</table></form>";

}
///////////////////// PROCESS ADD //////////////
if ($_GET['option']=='processadd') {

$st_link_name = stripinput($_POST['link_name']);
$st_link_description = stripinput($_POST['link_description']);

	$query5 = "INSERT into bo_links SET
				link_code = '".stripinput($_POST['link_code'])."',
				link_name = '".stripinput($st_link_name)."',
				link_address = '".stripinput($_POST['link_address'])."',
				link_description= '".stripinput($st_link_description)."',
				link_display_order = '".stripinput($_POST['link_display_order'])."',
				link_addedby = '".$_SESSION['user_name']."',
				link_addeddate = '".date("Y-m-d H:i:s")."',
				link_active = '".$_POST['active']."' ";
	$result5 = mysqli_query($connection,$query5) or die ("Can't insert into blurbs");

	echo "<br><b>entry added</b><br><br>";

}
/////////////////////// EDIT //////////////////
if ($_GET['option']=='edit') {

	$query6 = "SELECT * from bo_links where link_id='".$_GET['selection']."'";
	$result6 = mysqli_query($connection,$query6) or die ("Can't get the selected blurb");
	$row6 = mysqli_fetch_array($result6);

	echo "<font class='h1'>edit link</font><hr>";
	echo "<form method='post' action='?option=processedit'>";
	echo "<table width='300' border=0 cellspacing=0 cellpadding=5>";
	echo "<input type='hidden' name='origid' value='".$row6['link_id']."'>";
	echo "<tr><td>Link Name</td><td><input class='field' type='text' name='link_name' size=45
			value = '".$row6['link_name']."'></td></tr>";
	echo "<tr><td>Link Address</td><td><input class='field' type='text' name='link_address' size=45
			value = '".$row6['link_address']."'></td></tr>";
	echo "<tr><td>Description</td><td><textarea name='link_description' rows='6' cols='40'>";
	echo $row6['link_description']."</textarea></td></tr>";
	echo "<tr><td>Link Type</td><td><select name='link_code'>
									<option value='a-sl'";
									if($row6['link_code']=="a-sl") { echo " selected "; }
									echo ">Schroon Lake</option>
									<option value='b-aa'";
									if($row6['link_code']=="b-aa") { echo " selected "; }
									echo ">Area Attraction</option>
									<option value='c-li'";
									if($row6['link_code']=="c-li") { echo " selected "; }
									echo ">Links of Interest</option>
									</select>
									</td></tr>";
	echo "<tr><td>Order</td><td><input type='text' name='link_display_order'
			value = '".$row6['link_display_order']."'></td></tr>";
	echo "<tr><td>Show</td><td><input type='radio' name='active' value='Y'";
			if($row6['link_active']=='Y') { echo " checked "; }
	echo ">Y<br>
		 <input type='radio' name='active' value='N'";
			if($row6['link_active']=='N') { echo " checked "; }
	echo ">N</td></tr>";
	echo "<tr><td>&nbsp;</td><td></td></tr>";
	echo "<tr><td>&nbsp;</td>
		<td><input class='button' type='submit' name='submit' value='edit entry'></td></tr>";
	echo "</table></form>";

}
/////////////////////// PROCESS EDIT /////////
if ($_GET['option']=='processedit') {

$st_link_name = stripinput($_POST['link_name']);
$st_link_description = stripinput($_POST['link_description']);

	$query5 = "UPDATE bo_links SET
				link_code = '".stripinput($_POST['link_code'])."',
				link_name = '".stripinput($st_link_name)."',
				link_address = '".stripinput($_POST['link_address'])."',
				link_description= '".stripinput($st_link_description)."',
				link_display_order = '".stripinput($_POST['link_display_order'])."',
				link_editedby = '".$_SESSION['user_name']."',
				link_editeddate = '".date("Y-m-d H:i:s")."',
				link_active = '".$_POST['active']."'
			  WHERE link_id ='".$_POST['origid']."' ";
	$result5 = mysqli_query($connection,$query5) or die ("Can't insert into blurbs");

	echo "<br><b>entry modified</b><br><br>";

}
////////////////////// DELETE //////////////
if ($_GET['option']=='delete') {

	$queryfind = "SELECT link_name from bo_links WHERE link_id='".$_GET['selection']."'";
	$resultfind = mysqli_query($connection,$queryfind) or die ("Can't find the link");
	$findrow = mysqli_fetch_array($resultfind);

	$query11 = "DELETE from bo_links WHERE link_id='".$_GET['selection']."'";
	$result11 = mysqli_query($connection,$query11) or die ("Can't delete the link");

	echo "<br><b>entry removed</b><br><br>";

}
/////////////////////// LIST ///////////////
if($_GET['option']!="add" && $_GET['option']!="edit") {

	if(!$_GET['sort']) { $sortby = "link_active DESC,";} else {$sortby= $_GET['sort'].","; }

	$query = "SELECT * from bo_links ORDER BY $sortby link_name, link_addeddate, link_id DESC";
	$result = mysqli_query($connection,$query) or die ('error:'.mysqli_error()."<br/><br/>".$query);
	$nrow = mysqli_num_rows($result);

if($nrow>0) {

	$rowcolor="#005E2F";

	echo "<table cellpadding='3' cellspacing='0' border='0' width='970'>";
	echo "<tr class='shadow'>";
	echo "<td class='listhead'>&nbsp;</td>";
	echo "<td class='listhead'><a href='?sort=link_name'>Name</a></td>";
	echo "<td class='listhead'><a href='?sort=link_address'>Address</a></td>";
	//echo "<td class='listhead'>Description</td>";
	echo "<td class='listhead'><a href='?sort=link_code'>Type</a></td>";
	//echo "<td class='listhead'>Order</td>";
	echo "<td class='listhead'><a href='?sort=link_active'>Show</a></td>";
	echo "<td class='listhead'>&nbsp;</td>";
	echo "</tr>";

	while($row=mysqli_fetch_array($result)){
			echo "<tr ";
				if ($rowcolor=="#005E2F")
				{ $rowcolor="#007D3F";
				  echo " class='shadowalt' "; }
				elseif ($rowcolor=="#007D3F")
				{$rowcolor="#005E2F";
				echo " class='shadow' ";}
			echo ">";

	echo "<td><a href='?option=edit&selection=".$row['link_id']."'><img src='/backoffice/images/edit.png' height='24' border='0'></a></td>";
	echo "<td>".$row['link_name']."</td>";
	echo "<td>".$row['link_address']."</td>";
	//echo "<td>".$row['link_description']."</td>";
	echo "<td>";
		if($row['link_code']=="b-aa") { echo "Area"; }
		if($row['link_code']=="a-sl") { echo "Schroon"; }
		if($row['link_code']=="c-li") { echo "Interest"; }
	echo "</td>";
	//echo "<td>".$row['link_display_order']."</td>";
	echo "<td>";
		if($row['link_active']=="Y") { echo "<img src='/backoffice/images/active.png' height='24'>"; } else { echo "&nbsp;"; }
	echo "</td>";
	echo "<td><a href=\"?option=delete&selection=".$row['link_id']."\" onClick=\"return confirm('Are you sure you want to PERMANENTLY REMOVE this item (no recovery)? \\n');\"><img src='/backoffice/images/delete.png' height='24' border='0'></a></td>";
	echo "</tr>";

	}
	echo "</table>";
	}
}
//////////////////////////////////////////////////////////////////////////////////////////////
?>
<br /><br />
</div>
</body>
</html>
<? }?>
