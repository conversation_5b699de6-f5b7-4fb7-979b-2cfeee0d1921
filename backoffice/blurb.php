<?php
include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
session_start();

$today = date("Y-m-d");
$_GET['option'] ??= '';
$_GET['sort'] ??= '';

if($_SESSION['authorizednhr']!="yes") {

	if ($_GET['option']=="check") {

		if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
			$_SESSION['user'] = $_POST['username'];
			$_SESSION['authorizednhr'] = "yes";
			header ('Location: index.php');
		} else {
				session_destroy();
				$msg= "username/password combo not correct";
		}
	}

?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {

	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
						$name = str_replace(chr(145), "'", $name);
						$name = str_replace(chr(132), '"', $name);
						$name = str_replace("\"", "\\\"", $name);
    				$name = str_replace("'", "\'", $name);
            return $name;}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php');

	echo "<table width='970' cellpadding='0' cellspacing='0' border='0'>";
		echo "<tr>";
			echo "<td><h1>Site Text</h1></td>";
			echo "<form method='post' action='?option=add'>";
			echo "<td style='text-align:right;vertical-align:middle;'><input type='submit' value='add new text'></td>";
			echo "</form>";
		echo "</tr>";
	echo "</table>";

///////////////////////////////   ADD    /////////////////////////////
  if ($_GET['option'] == 'add')
		{
	   echo "<form method='post' action='?option=processadd'>";
	   echo "<table border='0' cellspacing='0' cellpadding='5'>";
	     echo "<tr><td>Type:</td>";
	     echo "<td><select name ='blurbtype'>";
	     	echo "<option value='links'>links page</option>";
	     	echo "<option value='fronttop'>front TOP</option>";
			echo "<option value='frontmid'>front MID</option>";
			echo "<option value='frontbottom'>front BOTTOM</option>";
		 	echo "<option value='info'>info page</option>";
         echo "</select></td></tr>";
	   echo "<tr><td>Title</td><td><input type='text' name='title'></td></tr>";
	   echo "<tr><td>Text</td><td>";
	   echo "<textarea rows=10 cols=80 name='info'></textarea>";
  	   echo "</td></tr>";
  	   echo "<tr><td>Order</td><td><input type='text' name='orderno' size=5></td></tr>";
  	   echo "<tr><td>Show</td><td><select name='display'><br>
			<option value='Y'>Y</option>
			<option value='N'>N</option>
			</select></td></tr>";
	   echo "<tr><td>&nbsp;</td><td></td></tr>";
	   echo "<tr><td>&nbsp;</td>
		<td><input class='button' type='submit' name='submit' value='Add Entry'></td></tr>";
	   echo "</table></form>";
    exit;
  }

////////////////////////// PROCESS ADD ////////////////////////////////////
  if ($_GET['option'] == 'processadd')
		{
    $query = "INSERT into areainfo SET
    			title = '".stripinput($_POST['title'])."',
			info = '".stripinput($_POST['info'])."',
			blurbtype = '".stripinput($_POST['blurbtype'])."',
			webaddress = '".stripinput($_POST['webaddress'])."',
			orderno = '".stripinput($_POST['orderno'])."',
			display = '".stripinput($_POST['display'])."'
			";
	$result = mysqli_query($connection,$query) or die ("error: ".mysqli_error()."<br /><br />".$query);
  }

///////////////////////////   EDIT  /////////////////////////
  if ($_GET['option']=="edit")
		{
	$q = "SELECT * FROM areainfo WHERE uniqueid = '".$_GET['selection']."'";
   	$r = mysqli_query($connection,$q) or die ("error: ".mysqli_error()."<br /><br />".$q);
	$n = mysqli_num_rows($r);

	if($n>0) {
		$row = mysqli_fetch_array($r);
		echo "<form method='post' action='?option=processedit'>";
		echo "<input type='hidden' name='uniqueid' value='".$row['uniqueid']."'>";
		echo "<table border='0' cellspacing='0' cellpadding='5'>";
	     echo "<tr><td>Type:</td>";
	     	echo "<td><select name ='blurbtype'>";
	    			echo "<option value='links'";
					if($row['blurbtype']=="links") { echo " selected "; }
				echo ">links page</option>";
	     		echo "<option value='fronttop'";
					if($row['blurbtype']=="fronttop") { echo " selected "; }
				echo ">front TOP</option>";
				echo "<option value='frontmid'";
					if($row['blurbtype']=="frontmid") { echo " selected "; }
				echo ">front MID</option>";
				echo "<option value='frontbottom'";
					if($row['blurbtype']=="frontbottom") { echo " selected "; }
				echo ">front BOTTOM</option>";
				echo "<option value='info'";
					if($row['blurbtype']=="info") { echo " selected "; }
				echo ">info page</option>";
			echo "</select></td></tr>";
		echo "<tr><td>Title</td><td><input type='text' name='title' value='".$row['title']."'></td></tr>";
		echo "<tr><td>Text</td><td>";
		echo "<textarea cols=80 rows=10 name='info'>".$row['info']."</textarea>";
		echo "</td></tr>";
		echo "<tr><td>Order</td><td><input type='text' name='orderno' value='".$row['orderno']."' size=5></td></tr>";
		echo "<tr><td>Show</td><td><select name='display'><br>
					<option value='Y'";
					if($row['display']=="Y") { echo " selected "; }
					echo ">Y</option>
					<option value='N'";
					if($row['display']=="N") { echo " selected "; }
					echo ">N</option>
		</select></td></tr>";
		echo "<tr><td>&nbsp;</td><td></td></tr>";
		echo "<tr><td>&nbsp;</td>
		<td><input class='button' type='submit' name='submit' value='edit'></td></tr>";
		echo "</table></form>";
	exit;
	}
  }

////////////////////////////  PROCESS EDIT  ///////////////////////////////
  if ($_GET['option'] == 'processedit')
		{
    $query= "UPDATE areainfo SET
        		title = '".stripinput($_POST['title'])."',
			info = '".stripinput($_POST['info'])."',
			blurbtype = '".stripinput($_POST['blurbtype'])."',
			webaddress = '".stripinput($_POST['webaddress'])."',
			orderno = '".stripinput($_POST['orderno'])."',
			display = '".stripinput($_POST['display'])."'
		WHERE uniqueid='".$_POST['uniqueid']."' ";

	$result = mysqli_query($connection,$query) or die ("error: ".mysqli_error()."<br /><br />".$query);
  }

////////////////////////// DELETE //////////////////////////
  if ($_GET['option'] == 'delete')
		{
   	$query = "DELETE FROM areainfo where uniqueid='".$_GET['selection']."' ";
	$result = mysqli_query($connection,$query) or die ("error: ".mysqli_error()."<br /><br />".$query);
  }

//////////// LIST //////////////////////
	if (!$_GET['sort']) { $sortby = "display DESC, uniqueid DESC"; }
	else { $sortby = $_GET['sort'].", uniqueid DESC";}

	$query = "SELECT * FROM areainfo ORDER BY ".$sortby." ";
  	$result = mysqli_query($connection,$query) or die ("error: ".mysqli_error()."<br /><br />".$query);
 	$nrows = mysqli_num_rows($result);

	if($nrows>0) {

 	 echo "<table width='970' cellpadding='8' cellspacing='0' border='0'>";
	 echo "<tr class='shadow'>
	 		<td></td>
			<td><a href='?sort=blurbtype'>Type</a></td>
			<td>Blurb</td>
			<td><a href='?sort=orderno'>Order</a></td>
			<td><a href='?sort=display'>Show</a></td>
			<td></td>
		<tr>";

	$rowcolor="#005E2F";

	while($row=mysqli_fetch_array($result)){
			echo "<tr ";
				if ($rowcolor=="#005E2F")
				{ $rowcolor="#007D3F";
				  echo " class='shadowalt' "; }
				elseif ($rowcolor=="#007D3F")
				{$rowcolor="#005E2F";
				echo " class='shadow' ";}
			echo ">";

		  echo "<td style='vertical-align:top;'><a href='?option=edit&selection=".$row['uniqueid']."'><img src='/backoffice/images/edit.png' height='24' border='0'></a></td>";
		  echo "<td style='vertical-align:top;'>".$row['blurbtype']."</td>";
		  echo "<td style='vertical-align:top;'>";
		  if($row['title']!="") {echo "<strong>".$row['title']."</strong><br /><br />"; }
		  echo nl2br($row['info']);
		  echo "</td>";
		  echo "<td style='vertical-align:top;'>".$row['orderno']."</td>";
		  echo "<td style='vertical-align:top;'>";
			if($row['display']=="Y") { echo "<img src='/backoffice/images/active.png' height='24'>"; } else { echo "&nbsp;"; }
		  echo "</td>";
		  echo "<td style='vertical-align:top;'><a href='?option=delete&selection=".$row['uniqueid']."' onClick=\"return confirm('Are you sure you want to PERMANENTLY REMOVE this item (no recovery)? \\n');\"><img src='/backoffice/images/delete.png' height='24' border='0'></a></td>";
		  echo "</tr>";

		}
	echo"</table>";
	}
  //////////////
?>
<br /><br />
</div>
</body>
</head>
</html>
<?
}
?>
