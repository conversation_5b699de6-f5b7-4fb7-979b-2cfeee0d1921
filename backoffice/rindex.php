<?php
include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
session_start();

$today = date("Y-m-d");
$_GET['option'] ??= '';
$_GET['sort'] ??= '';

if($_SESSION['authorizednhr']!="yes") {

	if ($_GET['option']=="check") {

		if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
			$_SESSION['user'] = $_POST['username'];
			$_SESSION['authorizednhr'] = "yes";
			header ('Location: index.php');
		} else {
				session_destroy();
				$msg= "username/password combo not correct";
		}
	}

?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {

	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            return $name;}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php');

	echo "<table width='970' cellpadding='0' cellspacing='0' border='0'>";
		echo "<tr>";
			echo "<td><h1>Rental Listings</h1></td>";
			echo "<form method='post' action='radd.php'>";
			echo "<td style='text-align:right;vertical-align:middle;'><input type='submit' value='add new rental'></td>";
			echo "</form>";
		echo "</tr>";
	echo "</table>";

if($_GET['option']=="add" || $_GET['option']=="edit") {

	if($_GET['option'] == "edit") {
		$query.=" UPDATE ";
	} else {
		$query.=" INSERT INTO ";
	}

	$type5 = $_REQUEST['type1']."|".$_REQUEST['type2'];

	$query.= "rental SET
    				listingno='".stripinput($_REQUEST['listingno'])."',
				toptext='".stripinput($_REQUEST['toptext'])."',
				type = '".$type5."',
				address='".stripinput($_REQUEST['address'])."',
				city='".stripinput($_REQUEST['city'])."',
				state='".stripinput($_REQUEST['state'])."',
				zip='".stripinput($_REQUEST['zip'])."',
				contactname='".stripinput($_REQUEST['contactname'])."',
				contactaddress='".stripinput($_REQUEST['contactaddress'])."',
				contactcity='".stripinput($_REQUEST['contactcity'])."',
				contactstate='".stripinput($_REQUEST['contactstate'])."',
				contactzip='".stripinput($_REQUEST['contactzip'])."',

				listingdate='".stripinput($_REQUEST['listingdate'])."',
				expdate='".stripinput($_REQUEST['expdate'])."',
				price='".stripinput($_REQUEST['price'])."',
				per='".stripinput($_REQUEST['per'])."',

				bed='".stripinput($_REQUEST['bed'])."',
				bath='".stripinput($_REQUEST['bath'])."',
				noofguest='".stripinput($_REQUEST['noofguest'])."',
				beach='".stripinput($_REQUEST['beach'])."',
				comments='".stripinput($_REQUEST['comments'])."',
				listingagent='".stripinput($_REQUEST['listingagent'])."',

				datesavail='".stripinput($_REQUEST['datesavail'])."',
				sqfoot='".stripinput($_REQUEST['sqfoot'])."',
				association='".stripinput($_REQUEST['association'])."',
				dock='".stripinput($_REQUEST['dock'])."',
				whatrights='".stripinput($_REQUEST['whatrights'])."',
				views='".stripinput($_REQUEST['views'])."',

				display='".stripinput($_REQUEST['display'])."' ";

		if($_GET['option'] == "edit") {
			$query.=" WHERE uniqueid='".$_REQUEST['uniqueid']."' ";
		}

        $results = mysqli_query($connection,$query) or die ("error: ".mysqli_error()."<br /><br />".$query);

	   echo "Rental ".$_REQUEST['listingno']." Updated<br/><br/>";
}


if($_GET['option']=="delete") {

	$q1 = "SELECT listingno FROM rental WHERE uniqueid = '".$_GET['sel']."' ";
	$r1 = mysqli_query($connection,$q1) or die ("error:".mysqli_error()."<br /><br />".$q1);
	$n1 = mysqli_num_rows($r1);
	if($n1>0) { $row1 = mysqli_fetch_array($r1); }

	// get photos
	$q2 = "SELECT * from bo_photos WHERE bo_photo_listing = '".$row1['listingno']."' ";
	$r2 = mysqli_query($connection,$q2) or die ("error:".mysqli_error()."<br /><br />".$q2);
	$n2 = mysqli_num_rows($r2);
	if($n2>0) {

		$conn_id = ftp_connect($ftp_server);
		$login_result = ftp_login($conn_id, "$ftp_user", "$ftp_pass");
		if ((!$conn_id) || (!$login_result)) {
			echo "Ftp connection has failed!<br>";
			echo "Attempted to connect to $ftp_server";
			die;
		}

		while($row2=mysqli_fetch_array($r2)){
			$al_picturesm = substr($row2['bo_photo_file'], 0, strlen($row2['bo_photo_file'])-4)."sm.jpg";
			$dftp_filepath = "public_html/photos/".$row2['bo_photo_file'];
			$dftp_filepath2 = "public_html/photos/".$al_picturesm;

			//echo $dftp_filepath."<br /><br />".$dftp_filepath2;

			$delete = ftp_delete($conn_id, "$dftp_filepath");
			$delete2 = ftp_delete($conn_id, "$dftp_filepath2");
		}
	}

	// delete from listing table
	$q3 = "DELETE from rental WHERE uniqueid = '".$_GET['sel']."' ";
	//echo $q3."<br /><br />";
	$r3 = mysqli_query($connection,$q3) or die ("error:".mysqli_error()."<br /><br />".$q3);

	// delete from photo table
	$q4 = "DELETE from bo_photos WHERE bo_photo_listing = '".$row1['listingno']."' ";
	//echo $q4."<br /><br />";
	$r4 = mysqli_query($connection,$q4) or die ("error:".mysqli_error()."<br /><br />".$q4);
}




	if ($_GET['sort']!=""){$sortby = "ORDER BY ".$_GET['sort']." ,listingno";}
	else {$sortby = "ORDER BY display DESC, listingno";}

	$q = "SELECT * FROM rental ".$sortby." ";
	$r = mysqli_query($connection,$q) or die ("error: ".mysqli_error()."<br /><br>");
	$n = mysqli_num_rows($r);

	if($n>0){
		echo "<table width='970' border='0' cellspacing='0' cellpadding='3'>";
			echo "<tr class='shadow'>";
				echo "<td>&nbsp;</td>";
				echo "<td>&nbsp;</td>";
				echo "<td><a href='?sort=listingno'>ID</a></td>";
				echo "<td><a href='?sort=address'>Address</a></td>";
				echo "<td><a href='?sort=city'>Location</a></td>";
				echo "<td><a href='?sort=price'>Price/Per</a></td>";
				echo "<td><a href='?sort=listingdate'>Listing Date</a></td>";
				echo "<td><a href='?sort=expdate'>Expiration</a></td>";
				// echo "<td><a href='?sort=listingagent'>Listing Agent</a></td>";
				echo "<td><a href='?sort=display DESC'>Show</a></td>";
				echo "<td>&nbsp;</td>";
			echo "</tr>";

		$rowcolor="#005E2F";

		while($row=mysqli_fetch_array($r)){
			$f_price = number_format($row['price'],0);

			echo "<tr ";
				if ($rowcolor=="#005E2F")
				{ $rowcolor="#007D3F";
				  echo " class='shadowaltr' "; }
				elseif ($rowcolor=="#007D3F")
				{$rowcolor="#005E2F";
				echo " class='shadowr' ";}
			echo ">";

				echo "<td><a href='addpic.php?selection=".$row['listingno']."'><img src='/backoffice/images/photo.png' height='24' border='0'</a></td>";
				echo "<td>";
					$q2 = "SELECT COUNT(bo_photo_id) as noofp FROM bo_photos WHERE bo_photo_listing = '".$row['listingno']."' ";
					$r2 = mysqli_query($connection,$q2) or die ("error: ".mysqli_error()."<br /><br/>");
					$n2 = mysqli_num_rows($r2);
					if($n2>0){
						$row2 = mysqli_fetch_array($r2);
						if($row2['noofp']!=0) {
							echo "<strong style='font-size:120%;'>".$row2['noofp']."</strong>";
						} else {
							echo "&nbsp;";
						}
					}
				echo "</td>";
				echo "<td><a href='radd.php?sel=".$row['uniqueid']."'>".$row['listingno']."</a></td>";

				echo "<td>".$row['address']."</td>";
				echo "<td>".$row['city']."</td>";
				echo "<td nowrap='nowrap'>$ ".$f_price."</td>";
				echo "<td>".$row['listingdate']."</td>";
				echo "<td>".$row['expdate']."</td>";
				//echo "<td>".$row['listingagent']."</td>";
				echo "<td>";
					if($row['display']=="Y") { echo "<img src='/backoffice/images/active.png' height='24'>"; } else { echo "&nbsp;"; }
				echo "</td>";
				echo "<td><a href='?option=delete&sel=".$row['uniqueid']."' onClick=\"return confirm('Are you sure you want to PERMANENTLY REMOVE this item (no recovery)? \\n');\"><img src='/backoffice/images/delete.png' height='24' border='0'></a></td>";
			echo "</tr>";
			}

		echo "</table>";
	}
?>
<br /><br />
</body>
</html>
<?
}
?>
