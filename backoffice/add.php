<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');
	include ($_SERVER['DOCUMENT_ROOT'].'/includes/session.inc.php');
	session_start();
  	$today = date("Y-m-d");


	if($_SESSION['authorizednhr']!="yes") {

		if ($_GET['option']=="check") {

			if($_POST['username']=="nhrbackoffice" && $_POST['password2']=="intutd") {
				$_SESSION['user'] = $_POST['username'];
				$_SESSION['authorizednhr'] = "yes";
				header ('Location: index.php');
			} else {
				 session_destroy();
				 $msg= "username/password combo not correct";
			}
		}

  ?>
<html>
<head>
<title>nhr backoffice</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
</head>
<body>
	<br><br><br><br>
	<center>
	<?php if ($msg!="")  {echo $msg; $msg="";} ?>
	<table width="300" cellpadding="0" cellspacing="3" border="0">
	<form action="index.php?option=check" method="post" name="loginform">
	<tr>
		<td colspan="2" class='header' style="text-align:center;vertical-align:middle;height:30px;">login</td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td style='text-align:right'>username&nbsp;&nbsp;&nbsp;</td>
		<td><input type='text' name="username" size=14 class="field"></td>
	</tr>
	<tr>
		<td style='text-align:right'>password&nbsp;&nbsp;&nbsp;</td>
		<td><input type='password' name="password2" size=14 class="field"></td>
	</tr>
	<tr>
		<td colspan="2"><img src="/images/spacer.gif" height="10"></td>
	</tr>
	<tr>
		<td colspan="2" class="header" style="text-align:center;vertical-align:middle;height:30px;">
			<input type="submit" name="submit" value="log in" class='button'></td>
	</tr>
	</form>
	</table>
	</center>
</body>
</html>
<?php
	exit;
	} elseif ($_SESSION['authorizednhr']=="yes") {

	/////////////////////////////////////////////////////////////////////////////////////////////
	function stripinput($name){
            $name = stripslashes($name);
            $name = str_replace('"','&#34;', $name);
            $name = str_replace("'",'&#39;', $name);
            return $name;}

	// Helper function to safely get array values with default
	function getValue($array, $key, $default = '') {
		return isset($array[$key]) ? $array[$key] : $default;
	}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<html>
<head>
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/backoffice/includes/backoffice.css">
<style>
.r	{text-align:right; border-right:1px dotted #ffffcc;}
#addform tr:nth-child(odd)		{ background-image: url(/images/rowhead.png);}
#addform tr:nth-child(even)		{  }
#addform td	{vertical-align:middle;}
</style>
</head>
<body>
<div style="width:970px;margin:0 auto;padding:0px 10px;">
<?php
	include ($_SERVER['DOCUMENT_ROOT'].'/backoffice/includes/header.php');

	if(isset($_GET['sel']) && $_GET['sel']) {
		$todo="edit";
		$q = "SELECT * FROM listing WHERE uniqueid = '".$_GET['sel']."' ";
		$r = mysqli_query($connection,$q) or die ("error : ".mysqli_error($connection)."<br /><br />".$q);
		$n = mysqli_num_rows($r);

		if($n>0) { $row = mysqli_fetch_array($r); }

	} else {
		$todo="add";
		$row = array(); // Initialize empty array for add mode
		$n = 0; // Initialize $n for add mode
	}


	echo "<h1>Property Listings (".$todo.")</h1>";

	echo "<form method='post' action='index.php?option=".$todo."' ENCTYPE='multipart/form-data'>";
	if(isset($_GET['sel']) && $_GET['sel']) {
		echo "<input type='hidden' name='uniqueid' value='".getValue($row, 'uniqueid')."'>";
	}
	echo "<table border='0' cellspacing='0' cellpadding='3' id='addform'>";
		echo "<tr>";
			echo "<td class='r'>Listing</td>";
			echo "<td><input type='text' name='listingno' size='4' value='".getValue($row, 'listingno')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Description</td>";
			echo "<td><input type='text' name='toptext' size='60' value='".getValue($row, 'toptext')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Address</td>";
			echo "<td><input type='text' name='address' size='25' value='".getValue($row, 'address')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>City/State/Zip</td>";
			echo "<td><input type='text' name='city' size='20' value='".getValue($row, 'city')."'>/<input type='text' name='state' size='2' value='".getValue($row, 'state')."'>/<input type='text' name='zip' size='10' value='".getValue($row, 'zip')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>County</td>";
			echo "<td><input type='text' name='county' size='10' value='".getValue($row, 'county')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Contact Name</td>";
			echo "<td><input type='text' name='contactname' size='20' value='".getValue($row, 'contactname')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Contact Address</td>";
      		echo "<td><input type='text' name='contactaddress' size='20' value='".getValue($row, 'contactaddress')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Contact City/State/Zip</td>";
			echo "<td><input type='text' name='contactcity' size='20' value='".getValue($row, 'contactcity')."'>/<input type='text' name='contactstate' size='2' value='".getValue($row, 'contactstate')."'>/<input type='text' name='contactzip' size='10' value='".getValue($row, 'contactzip')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Name of Business</td>";
			echo "<td><input type='text' name='nameofbiz' size='20' maxlength='50' value='".getValue($row, 'nameofbiz')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Road Frontage</td>";
			echo "<td><input type='text' name='roadfront' size='10' value='".getValue($row, 'roadfront')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Listing Date</td>";
			echo "<td><input type='text' name='listingdate' size='10' value='";
			if($n>0) { echo getValue($row, 'listingdate'); } else { echo "0000-00-00"; }
			echo "'> (YYYY-MM-DD)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Expiration Date</td>";
			echo "<td><input type='text' name='expdate' size='10' value='";
				if($n>0) { echo getValue($row, 'expdate'); } else { echo "0000-00-00"; }
			echo "'> (YYYY-MM-DD)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Sold Date</td>";
			echo "<td><input type='text' name='solddate' size='10' value='";
				if($n>0) { echo getValue($row, 'solddate'); } else { echo "0000-00-00"; }
			echo "'> (YYYY-MM-DD)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'><font color='gold'><b>Price</b></font></td>";
			echo "<td><input type='text' name='price' size='10' value='".getValue($row, 'price')."'> (numbers only - no decimal)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Owner Financing?</td>";
			echo "<td><select name='ownerfinance'>";
				echo "<option value='N'";
					if(getValue($row, 'ownerfinance')=="N") { echo "selected"; }
				echo ">N</option>";
				echo "<option value='Y'";
					if(getValue($row, 'ownerfinance')=="Y") { echo " selected ";}
				echo ">Y</option>";

			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Taxes</td>";
			echo "<td><input type='text' name='taxes' size='10' value='".getValue($row, 'taxes')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Zone</td>";
			echo "<td>";
				echo "<input type='checkbox' name='zone1' value='res'";
					if(strstr(getValue($row, 'zone'),'res')) { echo " checked "; }
				echo ">res<br />";
				echo "<input type='checkbox' name='zone2' value='com'";
					if(strstr(getValue($row, 'zone'),'com')) { echo " checked "; }
				echo ">com<br />";
				echo "<input type='checkbox' name='zone3' value='camp'";
					if(strstr(getValue($row, 'zone'),'camp')) { echo " checked "; }
				echo ">camp<br />";
				echo "<input type='checkbox' name='zone4' value='farm'";
					if(strstr(getValue($row, 'zone'),'farm')) { echo " checked "; }
				echo ">farm";
			echo "</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Vacant Land?</td>";
			echo "<td><select name='vacant'>";
				echo "<option value='N'";
					if(getValue($row, 'vacant')=="N") { echo " selected "; }
				echo ">N </option>";
				echo "<option value='Y'";
					if(getValue($row, 'vacant')=="Y") { echo " selected "; }
				echo ">Y</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Property Size</td>";
			echo "<td><input type='text' name='propsize' size='10' value='".getValue($row, 'propsize')."'> acres (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Age</td>";
			echo "<td><input type='text' name='age' size='10' value='".getValue($row, 'age')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No of Bedrooms</td>";
			echo "<td><input type='text' name='bed' size='4' value='".getValue($row, 'bed')."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No of Bathrooms</td>";
			echo "<td><input type='text' name='bath' size='4' value='".getValue($row, 'bath')."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Square Feet</td>";
			echo "<td><input type='text' name='sqfoot' size='10' value='".getValue($row, 'sqfoot')."'> (numbers only)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Design</td>";
			echo "<td><input type='text' name='design' size='10' value='".getValue($row, 'design')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Basement</td>";
			echo "<td><input type='text' name='basement' size='10' value='".getValue($row, 'basement')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Garage</td>";
			echo "<td><input type='text' name='garage' size='10' value='".getValue($row, 'garage')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Electric</td>";
			echo "<td><input type='text' name='electric' size='10' value='".getValue($row, 'electric')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Water</td>";
			echo "<td><input type='text' name='water' size='10' value='".getValue($row, 'water')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Sewer</td>";
			echo "<td><input type='text' name='sewer' size='10' value='".getValue($row, 'sewer')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Heat</td>";
			echo "<td><input type='text' name='heat' size='10' value='".getValue($row, 'heat')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Additional Heat</td>";
			echo "<td><select name='addheat'>";
				echo "<option value='n'";
					if(getValue($row, 'addheat')=="n") { echo " selected "; }
				echo ">N/A</option>";
				echo "<option value='fp'";
					if(getValue($row, 'addheat')=="fp") { echo " selected "; }
				echo ">Fireplace</option>";
				echo "<option value='ws'";
					if(getValue($row, 'addheat')=="ws") { echo " selected "; }
				echo ">Woodstove</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Rights</td>";
			echo "<td><select name='whatrights'>";
				echo "<option value='n'";
					if(getValue($row, 'whatrights')=="n") { echo " selected "; }
				echo ">n/a</option>";
				echo "<option value='lf'";
					if(getValue($row, 'whatrights')=="lf") { echo " selected "; }
				echo ">Lakefront</option>";
				echo "<option value='lr'";
					if(getValue($row, 'whatrights')=="lr") { echo " selected "; }
				echo ">Lakerights</option>";
				echo "<option value='bf'";
					if(getValue($row, 'whatrights')=="bf") { echo " selected "; }
				echo ">Brookfront</option>";
				echo "<option value='rf'";
					if(getValue($row, 'whatrights')=="rf") { echo " selected "; }
				echo ">Riverfront</option>";
				echo "<option value='pf'";
					if(getValue($row, 'whatrights')=="pf") { echo " selected "; }
				echo ">Pond Front</option>";
				echo "<option value='wv'";
					if(getValue($row, 'whatrights')=="wv") { echo " selected "; }
				echo">Water Views</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Views</td>";
			echo "<td><input type='text' name='views' size='20' value='".getValue($row, 'views')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Water Frontage</td>";
			echo "<td><input type='text' name='lakefrontage' size='10' value='".getValue($row, 'lakefrontage')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Beach</td>";
			echo "<td><input type='text' name='beach' size='10' value='".getValue($row, 'beach')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Dock</td>";
			echo "<td><select name='dock'>";
				echo "<option value='N'";
					if(getValue($row, 'dock')=="N") { echo " selected "; }
				echo ">N</option>";
				echo "<option value='Y'";
					if(getValue($row, 'dock')=="Y") { echo " selected "; }
				echo ">Y</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Association</td>";
			echo "<td><input type='text' name='association' size='15' value='".getValue($row, 'association')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Season</td>";
			echo "<td><select name='season'>";
				echo "<option value='yr'";
					if(getValue($row, 'season')=="yr") { echo " selected "; }
				echo ">year round</option>";
				echo "<option value='s'";
					if(getValue($row, 'season')=="s") { echo " selected "; }
				echo ">seasonal</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Type of Income Property</td>";
			echo "<td><input type='text' name='income' size='20' value='".getValue($row, 'income')."'> (blank if not a business)</td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>No. of Units</td>";
			echo "<td><input type='text' name='noofunits' size='10' value='".getValue($row, 'noofunits')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Comments</td>";
			echo "<td><textarea name='comments' rows='7' cols='50'>".getValue($row, 'comments')."</textarea></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Listing Agent</td>";
			echo "<td><input type='text' name='listingagent' size='10' value='".getValue($row, 'listingagent')."'></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Show</td>";
			echo "<td><select name='display'>";
				echo "<option value='Y'";
					if(getValue($row, 'display')=="Y") { echo " selected "; }
				echo ">Y</option>";
				echo "<option value='N'";
					if(getValue($row, 'display')=="N") { echo " selected "; }
				echo ">N</option>";
			echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td class='r'>Status</td>";
			echo "<td><select name='status'>";
				echo "<option value='active'";
					if(getValue($row, 'status')=="active") { echo " selected "; }
				echo ">active</option>";
				echo "<option value='pending'";
					if(getValue($row, 'status')=="pending") { echo " selected "; }
				echo ">pending</option>";
				echo "<option value='sold'";
					if(getValue($row, 'status')=="sold") { echo " selected "; }
				echo ">sold</option>";
				echo "</select></td>";
		echo "</tr>";
		echo "<tr>";
			echo "<td>&nbsp;</td>";
			echo "<td><input type='submit' value='".$todo." listing'></td>";
		echo "</tr>";
		echo "</table>";
		echo "</form>";
?>
<br/><br/>
</div>
</body>
</html>
<?
}
?>
