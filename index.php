<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="Northern Homes Realty Inc.  Your real estate connection for Schroon Lake, NY and surrounding areas.">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>
<body>
<?php include ($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" cellpadding="0" cellspacing="0" border="0">
	<tr> 
		<td class="sidenav2">
			<div id="sidenav">
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
		</td>
		<td width="100%">
          	<div align="center" id='content' style="text-align:center;">
               	<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
                    </div>
                    <br />
               <center>
			<table border="0" cellspacing="0" cellpadding="0" width="800">
				<tr>
					<td align="center">
						<img src="/images/newhouse2.png" width="350" height="228">
                         </td>
                         <td class="mid" style="text-align:center;">
                             <?php
					     $q2 = "SELECT * from areainfo WHERE display='Y' AND blurbtype='fronttop' ORDER BY orderno";
                              $r2 = mysqli_query($connection,$q2) or die ("error:".mysqli_error()."<br /><br />".$q2);
                              $n2 = mysqli_num_rows($r2);
                              
                              if($n2>0) { 
							while($row2 = mysqli_fetch_array($r2)){
								if($row2['title']) { echo "<h1>".$row2['title']."</h1>"; }
									echo "<blockquote>";
										echo nl2br($row2['info']);
									echo "</blockquote>";
								}
						}
						?>
					</td>
                    </tr>
			<?php
               
               $q = "SELECT * from areainfo WHERE display='Y' AND blurbtype='frontmid' ORDER BY orderno";
               $r = mysqli_query($connection,$q) or die ("error:".mysqli_error()."<br /><br />".$q);
               $n = mysqli_num_rows($r);
               
               if($n>0) {
				echo '<tr>';
				echo '<td colspan="2"><br />';
				while($row = mysqli_fetch_array($r)){
					if($row['title']) { echo "<h1>".$row['title']."</h1>"; }
						echo "<blockquote>";
							echo nl2br($row['info']);
						echo "</blockquote>";
				}
				echo '</td>';
				echo '</tr>';
			}
               ?>
				<tr> 
                    	<td colspan="2">
                         	<table cellpadding="5" cellspacing="0" border="0">
                              	<tr>
                              	<td><img src="/images/schraders2017.jpg" width="200" height="200"></td>
                                   <td>
							<?php
                                   $q3 = "SELECT * from areainfo WHERE display='Y' AND blurbtype='frontbottom' ORDER BY orderno";
                                   $r3 = mysqli_query($connection,$q3) or die ("error:".mysqli_error()."<br /><br />".$q3);
                                   $n3 = mysqli_num_rows($r3);
                                   
                                   if($n3>0) { 
                                        while($row3 = mysqli_fetch_array($r3)){
                                             if($row3['title']) { echo "<h1>".$row3['title']."</h1>"; }
                                                  echo "<blockquote>";
                                                       echo nl2br($row3['info']);
                                                  echo "</blockquote>";
                                             }
                                   }
                                   ?>
                                   </td>
							</tr>
						</table>
                              <hr>
                              <center>
                              <b><i>xNorthern Homes Realty is a member of the 
                              Schroon Lake Chamber of Commerce.<br />
                              For information about Schroon Lake, an all-season resort community 
                              visit <a href="http://www.schroonlakechamber.com">www.schroonlakechamber.com</a><br />
                              The surroundings are truly breathtaking!</i></b><i><br />
                              <br />
                              <b>Thanks for browsing our website! We look forward 
                              to hearing from you soon!</b></i> 
                              </center>
					</td>
				</tr>
			</table>
		<br />
          </center>
          </div>
		</td>
	</tr>
	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>