<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate  Listings : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="Northern Homes Realty Inc.  Your real estate connection for Schroon Lake, NY and surrounding areas.">
<meta name="keyword" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>

<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
     	<td class="sidenav2">
			<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
		</td>
		<td width="100%">
                <div align="center" id='content'>
               	<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
                    </div> 
				<?php

  				if ($sort!=""){$sort = "ORDER BY ".$sort;}
				else { $sort = " ORDER BY uniqueid DESC "; }
				
				if ($_GET['t']) { $search = " type LIKE '%".$_GET['t']."%' ";}
				else { $search = " uniqueid IS NOT NULL "; }
		
		
				$q = "SELECT * FROM rental WHERE ".$search." AND display='y' ".$sort. " ";
				$r = mysqli_query($connection,$q) or die ("error:".mysqli_error()."<br /><br />".$q);
				$n = mysqli_num_rows($r);
				?>
     
 			<strong>You can sort the listings by clicking on a column heading.</strong><br /><br />
        		Click on a Listing Number to see the detail of that listing.<br /><br /></div>
     		<table width="800" border="0" cellspacing="0" cellpadding="0" align="center">
        			<tr class="shadow"> 
          			<td></td>
		  			<td class='mid'><a href="?sort=listingno&t<?=$_GET['t'];?>">Listing No.</a></td>
		  			<td class='mid'><a href="?sort=toptext&t<?=$_GET['t'];?>">Description</a></td>
                         <td class='mid'><a href="?sort=city&t<?=$_GET['t'];?>">Location</a></td>
                         <td class='mid'><a href="?sort=price&t<?=$_GET['t'];?>">Rates</a></td>
        			</tr>
        		<?
			$curclass="lite";
			if($n>0) {	
			while($row = mysqli_fetch_array($r)){
		
			echo "<tr class='".$curclass."'>";

				echo "<td>";
				echo "<a href='/rdetail.php?selection=".$row['listingno']."&t=".$_GET['t']."'>";
		
				$qp = "SELECT * FROM bo_photos WHERE bo_photo_listing = '".$row['listingno']."' 
					AND bo_photo_primary = 'Y' ";
				$rp = mysqli_query($connection,$qp) or die ("error : ".mysqli_error()."<br /><br />".$qp);
				$np = mysqli_num_rows($rp);
				
				if($np>0) { 
					$rowp = mysqli_fetch_array($rp);
					$picsm = substr($rowp['bo_photo_file'], 0, strlen($rowp['bo_photo_file']) - 4)."sm.jpg";
					
					echo "<img src='/photos/".$picsm."' width='125' border='0'></a>";
				} else {
					echo "<img src='photos/unavailablesm.jpg' width='125' border='0'>";
				}
				echo "</a></td>";
				 
				echo "<td class='mid'><a href='/rdetail.php?selection=".$row['listingno']."&t=".$_GET['t']."' style='text-decoration:underline;'>".$row['listingno']."</a></td>";
				echo "<td class='mid'><font class='status'>".$row['toptext']."</font><br />";
					echo "<img src='/images/spacer.gif' height='10' width='10'><br />";
					echo "Bedrooms: ".$row['bed']." / Bathrooms: ".$row['bath']."<br />"; 
					echo "<img src='/images/spacer.gif' height='5' width='5'><br />";
					echo "Number of Guests: ".$row['noofguest'];

				echo "</td>";
				echo "<td class='mid'>".$row['city']."</td>";
				echo "<td class='mid'>$ ".number_format($row['price'],0)." per ".$row['per']."</td>";
				
				echo "</tr>";
				
				if ($curclass == "lite") { $curclass = "dark"; }
				elseif ($curclass == "dark") { $curclass = "lite";}

			}
			}
			?>
      		</table>
               <br />
               <br />
		</td>
	</tr>
	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>