body 	{ font: 12px V<PERSON><PERSON>, Arial, Helvetica; color: #FFFFCC; margin: 0px, 0px, 0px, 0px;
		  background: rgb(0,57,16) url(/images/18.jpg);}
		  
td  		{ font: 12px Verdana, Arial, Helvetica; color: #FFFFCC; vertical-align: top;}
blockquote {margin: 0px,0px,0px,20px;}
.b2		 {margin: 0px,0px,0px,30px;}

a 		{text-decoration: none; color: ffcc33; font-weight:bold;}
a:visted 	{color: #ffcc33;}
a:active	{color: #ffcc33;}
a:hover 	{text-decoration: underline;}


a.nav:link    {color: rgb(0,57,16); }
a.nav:visited {color: rgb(0,57,16);}
a.nav:hover   {text-decoration: underline;} 
a.nav:active  {color: rgb(0,57,16);}

.sidenav		{background: #FFCC33; }
.sidenav2		{background: #ffcc33 url(/images/newside.png); }

h1		{font: 20px Verdana, Arial, Helvetica; color:#ffffcc;}
h2		{font: 30px Verdana, Arial, Helvetica; color:#ffffcc;}
hx2		{font: 30px Verdana, Arial, Helvetica; color:#80ff33;}
.h4		{font: 30px Verdana, Arial, Helvetica; color:#ffcc33;}
.h3		{font: 14px Verdana, Arial, Helvetica; color:ffcc33; font-weight:bold;}

.mini	{font: 8px Verdana, Arial, Helvetica; color:#ffcc33;}
.shadow	{background-image: url(/images/rowhead.png);}
.shadow td {border-bottom:1px solid #fff;}
.shadow2  {background: rgb(255,93,93) url(/images/shadow2.jpg); }

.boxshadow	{
	box-shadow: 10px 10px 5px #003109;
	-moz-box-shadow: 10px 10px 5px #003109;
	-webkit-box-shadow: 10px 10px 5px #003109;
/* For IE 8 */
-ms-filter: "progid:DXImageTransform.Microsoft.Shadow(Strength=10, Direction=135, Color='#003109')";
/* For IE 5.5 - 7 */
filter: progid:DXImageTransform.Microsoft.Shadow(Strength=10, Direction=135, Color='#003109');
}

.gold	{color:#ffcc33;}
.mid		{vertical-align: middle; padding:10px;}
.lilbigger{font:14px Verdana, Arial, Helvetica;}

.footer	{background: url(/images/border.gif) bottom left repeat-x; vertical-align:bottom;}
.copyright { font:  bold 10px Arial; color: rgb(0,57,16); background: #ffcc33;}
.status	{font-weight: bold; color: #ffffcc;}
.soldstatus	{font-weight: bold; color: #FFCC33;}

hr		{border: 2px solid #ffcc33; padding:0px,0px,0px,0px;}

.linkhead	{font: 20px Verdana, Arial, Helvetica, sans-serif; color: #ffffff; text-decoration:underline; text-transform: uppercase; line-height:40px; }

.lite	{background: url(/images/rowback.png) top left;}
.lite td	{border-bottom: 1px solid #ffffcc;}
.dark td	{border-bottom: 1px solid #ffffcc;}

div#logo	{display: none;}

.photo {
	position: relative; 
}

.sold {
    background-image: url(/images/sold.png);
    background-position: top left;
    width:150px;
    height:120px;
}

.pending {
    background-image: url(/images/pending.png);
    background-position: top left;
    width:150px;
    height:120px;
}

.soldbig {
    background-image: url(/images/sold.png);
    background-position: top left;
    width:300px;
    height:240px;
}

.pendingbig {
    background-image: url(/images/pending.png);
    background-position: top left;
    width:300px;
    height:240px;
}

@media print{
body			{width:6.5in; }
div			{display: none;}
div#content	{display:block;}
div#logo		{display:block;}
}