<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate  Listings : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="Northern Homes Realty would like to share with you some links to area events and organizations that you might find helpful.">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>

<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr> 
		<td class="sidenav2">
			<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
			</div>
		</td>
		<td width="100%"> 
			<div align="center" id='content'>
				<div id='logo'>
                    <img src="/images/nhheader.jpg" width="625" height="110">
                    </div>
				<?php
                    echo "<table width='800' cellpadding='0' cellspacing='0' border='0'>";
                    echo "<tr><td><img src='/images/spacer.gif' width='20'></td>";
                    echo "<td>";
                    
                    $q = "SELECT * from areainfo WHERE display='Y' AND blurbtype='links' ORDER BY orderno";
                    $r = mysqli_query($connection,$q) or die ("error:".mysqli_error()."<br /><br />".$q);
                    $n = mysqli_num_rows($r);
                    
                    if($n>0) {
                    	while($row = mysqli_fetch_array($r)){
                    		echo "<h1>".$row['title']."</h1>";
                    		echo "<blockquote>";
                    			echo nl2br($row['info']);
                    		echo "</blockquote>";
                    	}
                    }
                    echo "<hr><blockquote>";
                    
                    $q2 = "SELECT * from bo_links WHERE link_active='Y' ORDER BY link_code, link_name";
                    $r2 = mysqli_query($connection,$q2) or die ("error:".mysqli_error()."<br /><br />".$q2);
                    $n2 = mysqli_num_rows($r2);
                    
                    if($n2>0) {
                    	echo "<blockquote>";
                    	echo "<table width='800' cellpadding='0' cellspacing='0' border='0'>";
                    	while($row2 = mysqli_fetch_array($r2)){
                    
                   			$newcatname = $row2['link_code'];
                    
                    		if ($newcatname != $catname) {
                    			$catname = $newcatname;
                    			switch ($newcatname) {
                    			case 'a-sl': 	echo"<tr><td><font class='linkhead'>Schroon Lake Attractions</font></td></tr>"; break;
                   				case 'b-aa':	echo"<tr><td><font class='linkhead'>Area Attractions</font></td></tr>"; break;
                    			case 'c-li':	echo"<tr><td><font class='linkhead'>Links of Interest</font></td></tr>"; break;
                    		}
                   		}

					echo "<tr><td>";
					echo "<li><a href='https://".$row2['link_address']."' target='_blank'>".$row2['link_name']."</a>";
					echo "&nbsp;&nbsp;( <a href='https://".$row2['link_address']."' target='_blank'>".$row2['link_address']."</a> )";
					
					
					if($row2['link_description']) { 
						echo "<br /><blockquote style='margin:0px 20px;'><i>".nl2br($row2['link_description'])."</i></blockquote>"; }
					echo "</li>";
					echo "</td></tr>";

					echo "<tr><td><img src='/images/spacer.gif' height='10'></td></tr>";
				}
			echo "</table>";
			echo "</blockquote>";
			}
		echo "</blockquote>";
		echo "</td>";
		echo "<td><img src='/images/spacer.gif' width='20'></td></tr>";
		echo "</table>";
		?>
          <br />
		</div>
		</td>
	</tr>
	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>