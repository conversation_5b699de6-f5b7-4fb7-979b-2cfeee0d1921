<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/connect.inc.php');?>
<html>
<head>
<title>Schroon Lake Real Estate  Listing Detail : Northern Homes Realty : Schroon Lake, NY</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="description" content="Northern Homes Realty Inc.  Your real estate connection for Schroon Lake, NY and surrounding areas.">
<meta name="keywords" content="Schroon Lake, Adirondack, Brant Lake, Paradox Lake, Adirondacks, Chestertown, North Hudson, North Creek, Loon Lake, Friends Lake, Elizabethtown, 
Westport, Newcomb, Pottersville, Eagle Lake, Lake Champlain, Ticonderoga, Warrensburg, Minerva, Upstate NY, Essex County, Warren County, Vacation Properties, Vacation Rentals, Vacation Homes, Adirondack Park Real Estate, Lakefront Properties, Lake Rights Properties, Lake George, Lake Placid, Paradox, Chestertown, Gore Mountain Region, Gore Mountain Vacation Rentals, Whiteface, Lake Champlain, Saranac Lake, Adirondack Realty, High Peaks, Adirondack land and acreage, Homes and properties in the Adirondack Park, Adirondack Mountains, Adirondack Real Estate, Adirondack Properties, Adirondack Vacation Homes, Adirondack Vacation Rentals, Upstate New York, Waterfront, Vacant Land, Lake Views, Log Homes, New Construction, Camps, Cottages, Townhouses, Businesses, Commerical Property, Farms, Investment Property, Income Property, Multi-Family, Winter Ski Rentals, Summer keywords" content="Rentals, Vacation Rentals on Schroon Lake, Real Estate on Schroon Lake, Real Estate for Sale, Homes For Sale, Residential Homes, Boating, Fishing, Cabins, Schroon River, Riverfront, Motel, Restaurant">
<LINK REL="STYLESHEET" TYPE="text/css" HREF="/includes/nhr.css">
<LINK REL="SHORTCUT ICON" HREF="/images/nhricon.ico">
</head>

<body>
<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/header.php'); ?>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
     	<td class="sidenav2">
			<div id='sidenav'>
				<?php include($_SERVER['DOCUMENT_ROOT'].'/includes/nav.php'); ?>
               </div>
		</td>
    		<td width="100%">
    			<div align="center" id="content">
          		<div id='logo'>
					<img src="/images/nhheader.jpg" width="625" height="110">
				</div> 
				<?php

				$qp = "SELECT * from bo_photos WHERE bo_photo_listing = '".$_GET['selection']."' 
			   		ORDER BY bo_photo_primary DESC, bo_photo_order LIMIT 2, 999999999";
				$rp = mysqli_query($connection,$qp) or die ("error : ".mysqli_error()."<br /><br />".$qp);
				$np = mysqli_num_rows($rp);
				$x = 1;
				
				while($rowp= mysqli_fetch_array($rp)){
				   $pic[$x]['pic'] = $rowp['bo_photo_file'];
				   $pic[$x]['cap'] = $rowp['bo_photo_caption'];
				   $pic[$x]['prime'] = $rowp['bo_photo_primary'];
				   $x=$x+1;
				}
				?>

				<h1>Pictures of Listing Number: <? echo $_GET['selection'];?></h1>
				
                    <table width="800" border="0" cellspacing="0" cellpadding="0">
					<tr> 
						<td colspan='3'> 
						<?php
                                   if($np>1) {
								$z=0;
                                        $y=2;
                                        $pt = '<table width="100%" border="0" cellspacing="0" cellpadding="5">';	   	
									while($y <= $np) {
                                             	if($z==0) { $pt.= '<tr>'; }
                                                  if($pic[$y]['pic']!="") {
                                                       $pt.= '<td width="50%">';
                                                       $picsm = substr($pic[$y]['pic'], 0, strlen($pic[$y]['pic']) - 4)."sm.jpg";
											$pt.= '<a href="/photos/'.$pic[$y]['pic'].'" target="_blank">';
                                                       $pt.= '<img src="/photos/'.$picsm.'" width="300" border="0" class="boxshadow">';
                                                       $pt.= '</a>';
											$pt.= '<br />';
											$pt.= $pic[$y]['cap']."<br />";
											$pt.= '</div>';
											$pt.= '</td>';
									}
								
								$z=$z+1;
								if($z==2) {
									$pt.="</tr>";
									$z=0;
								}
							$y=$y+1;
							}
							$pt.= '</table>';
						}
						echo $pt;
						?>
						</td>
                    	</tr>
					<tr><td>&nbsp;</td><td></td><td></td></tr>
					<tr> 
            				<td colspan="3"> 
              					<div align="center"><a href="javascript:history.go(-1)">Click here to return to listing</a></div>
            				</td>
          			</tr>
       			 </table>
                    <br />
                    <br />
                    For more information contact:<br />
                    Deirdre Schrader, Broker/Owner<br /><br />
                    NORTHERN HOMES REALTY, INC.<br />
                    889 US Route 9<br />
                    Schroon Lake, NY 12870 
                    <p>(************* (phone);&nbsp;&nbsp; (************* (fax)</p>
                    <p>email 
                    <a href="mailto:<EMAIL>" name="web inquiry - listing no <? echo "$listingno";?> " ><EMAIL></a> 
                    for more information</p>
      		</div>
    		</td>
	</tr>
  	<tr> 
		<td valign="bottom" colspan="2"><?php include($_SERVER['DOCUMENT_ROOT'].'/includes/footer.php'); ?></td>
	</tr>
</table>
</body>
</html>